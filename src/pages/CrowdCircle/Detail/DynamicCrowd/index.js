import React, { useState, useEffect } from 'react';
import { Card, Form, Input, DatePicker, Select, Button, message, Spin } from 'antd';
import { connect } from 'dva';
import { Link, history } from 'umi';
import { QuestionCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { createCrowd, queryCrowdCircle, updateCrowdCircle, checkCrowdCircleName } from '@/services/api';
import { getQueryParams } from '../../common/utils';
import AddCrowdGroupModal from '../../components/AddCrowdGroupModal';
import styles from './index.less';

const { TextArea } = Input;
const { Option } = Select;

const DynamicCrowd = props => {
  const {
    dispatch,
    crowdGroup: { dataList, modelConfig },
  } = props;

  const [form] = Form.useForm();
  const [crowdGroupForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [detailLoading, setDetailLoading] = useState(false);

  const { id, type } = getQueryParams();
  const isEdit = !!id;
  const isView = type === 'view';

  // 过期时间限制：不能早于当前时间，不能超过三个月
  const disabledDate = current =>
    current < dayjs().startOf('day') || current >= dayjs() + 91 * 24 * 60 * 60 * 1000;

  // 预设时间快捷选择
  const presetDates = (date) => {
    form.setFieldsValue({ expiredDate: date });
  };

  // 获取人群分组数据
  useEffect(() => {
    dispatch({
      type: 'crowdGroup/fetchData',
      payload: {
        pageNo: 1,
        pageSize: 500,
      },
    });
  }, [dispatch]);

  // 编辑时获取人群详情
  useEffect(() => {
    if (isEdit) {
      setDetailLoading(true);
      queryCrowdCircle(id).then(res => {
        if (res.success && res.data) {
          const data = res.data;
          form.setFieldsValue({
            crowdName: data.crowdName,
            crowdDescription: data.crowdDescription,
            expiredDate: data.expiredDate ? dayjs(data.expiredDate) : null,
            groupIds: data.groupIds || [],
          });
        }
        setDetailLoading(false);
      });
    }
  }, [id, isEdit, form]);

  // 新增分组处理
  const handleFinish = values => {
    const data = {
      ...values,
      type: 'CROWD',
    };
    if (modelConfig.isEdit) {
      data.id = modelConfig.record.key;
      data.name = modelConfig.record.title;
    }
    dispatch({
      type: 'crowdGroup/operateCrowdGroup',
      payload: { ...data, isEdit: modelConfig.isEdit },
    }).then(res => {
      if (res.success) {
        crowdGroupForm.resetFields();
      }
    });
  };

  // 表单提交
  const onFinish = values => {
    const payload = {
      ...values,
      crowdType: 'DYNAMIC_CROWD',
      physicalProfileCode: 'TAOBAO_USER',
      needUpdate: false,
      expiredDate: values.expiredDate.valueOf(),
      applyScenes: ['MATCH'],
    };

    if (isEdit) {
      payload.id = id;
    }

    setLoading(true);
    const request = isEdit ? updateCrowdCircle : createCrowd;

    request(payload).then(res => {
      if (res.success) {
        message.success(isEdit ? '更新成功' : '创建成功');
        history.push('/crowd-stategy/gather-person');
      } else {
        message.error(res.msg || (isEdit ? '更新失败' : '创建失败'));
      }
      setLoading(false);
    });
  };

  return (
    <Spin spinning={detailLoading}>
      <Form form={form} onFinish={onFinish} layout="vertical">
        <div className={styles.container}>
          <Card
            title="人群信息"
            className={styles.centerCard}
            extra={
              <a target="_blank" href="https://aliyuque.antfin.com/qnwrq9/kzl68s/iadc1sxwfgwh18iu#">
                教程文档 &gt;
              </a>
            }
          >
            <Form.Item
              name="crowdName"
              label="人群名称"
              rules={[
                ({ getFieldValue }) => ({
                  async validator(_, value) {
                    if (!value) {
                      return Promise.reject(new Error('人群名称不能为空'));
                    }
                    const data = {
                      name: value,
                    };
                    if (id) {
                      data.id = id;
                    }
      
                    const result = await checkCrowdCircleName(data);
      
                    if (result.data) {
                      return Promise.reject(new Error('人群名称已存在'));
                    }
      
                    return Promise.resolve();
                  },
                }),
              ]}
              required
            >
              <Input placeholder="请输入人群名称" disabled={isView}/>
            </Form.Item>

            <Form.Item
              name="crowdDescription"
              label="人群描述"
              rules={[{ required: true, message: '请输入人群描述' }]}
            >
              <TextArea
                placeholder="请输入人群描述"
                rows={3}
                maxLength={200}
                disabled={isView}
              />
            </Form.Item>

            <Form.Item 
              label="人群过期时间"
              name="expiredDate"
              rules={[{ required: true, message: '请选择人群人群过期时间' }]}>
                <DatePicker
                  format="YYYY-MM-DD HH:mm:ss"
                  disabledDate={disabledDate}
                  showTime={{
                    defaultValue: dayjs('23:59:59', 'HH:mm:ss'),
                  }}
                  placeholder="请选择人群过期时间"
                  renderExtraFooter={() => [
                    <Button key="oneWeek" onClick={() => presetDates(dayjs(dayjs() + 7 * 24 * 60 * 60 * 1000))} type="link">一星期</Button>,
                    <Button key="oneMonth" onClick={() => presetDates(dayjs(dayjs() + 30 * 24 * 60 * 60 * 1000))} type="link">一个月</Button>,
                    <Button key="threeMonth" onClick={() => presetDates(dayjs(dayjs() + 90 * 24 * 60 * 60 * 1000))} type="link">三个月</Button>
                  ]}
                  showToday={false}
                />
            </Form.Item>

            <Form.Item
              name="groupIds"
              label="人群分组"
              rules={[{ message: '请选择人群分组' }]}
            >
              <div style={{ display: 'flex', gap: '8px' }}>
                <Select
                  mode="multiple"
                  placeholder="请选择人群分组"
                  style={{ flex: 1 }}
                  disabled={isView}
                >
                  {(dataList || []).map(group => (
                    <Option key={group.key} value={group.key}>
                      {group.title}
                    </Option>
                  ))}
                </Select>
                {!isView && (
                  <Button
                    type="primary"
                    onClick={() => {
                      dispatch({
                        type: 'crowdGroup/updateState',
                        payload: {
                          modelConfig: {
                            visible: true,
                            isEdit: false,
                            record: {},
                          },
                        },
                      });
                    }}
                  >
                    新增分组
                  </Button>
                )}
              </div>
            </Form.Item>

            <div className={styles.buttonGroup}>
              {!isView && (
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  style={{ marginRight: 12 }}
                >
                  提交
                </Button>
              )}
              <Link to="/crowd-stategy/gather-person">
                <Button>取消</Button>
              </Link>
            </div>
          </Card>
        </div>

        {/* 新增分组弹窗 */}
        {modelConfig.visible && (
          <AddCrowdGroupModal handleFinish={handleFinish} form={crowdGroupForm} />
        )}
      </Form>
    </Spin>
  );
};

export default connect(({ crowdGroup }) => ({
  crowdGroup,
}))(DynamicCrowd);
